<?php

namespace LBCDev\OAuthFileExplorer\Services\Explorers;

use LBCDev\OAuthFileExplorer\Services\AbstractFileExplorer;
use Microsoft\Graph\GraphServiceClient;
use Microsoft\Kiota\Authentication\Oauth\AccessTokenContext;

class OneDriveExplorer extends AbstractFileExplorer
{
    protected ?GraphServiceClient $graphClient = null;

    public function getServiceType(): string
    {
        return 'onedrive';
    }

    protected function initializeService(): void
    {
        $this->ensureValidToken();

        $tokenRequestContext = new AccessTokenContext($this->oauthService->access_token);
        $this->graphClient = new GraphServiceClient($tokenRequestContext);
    }

    public function listFiles(string $folderId = 'root', int $pageSize = 50): array
    {
        $this->ensureValidToken();

        if (!$this->graphClient) {
            $this->initializeService();
        }

        try {
            // Build the request path
            if ($folderId === 'root') {
                $requestBuilder = $this->graphClient->me()->drive()->root()->children();
            } else {
                $requestBuilder = $this->graphClient->me()->drive()->items()->byDriveItemId($folderId)->children();
            }

            // Configure request options
            $requestConfiguration = new \Microsoft\Graph\Generated\Me\Drive\Root\Children\ChildrenRequestBuilderGetRequestConfiguration();
            $requestConfiguration->queryParameters = new \Microsoft\Graph\Generated\Me\Drive\Root\Children\ChildrenRequestBuilderGetQueryParameters();
            $requestConfiguration->queryParameters->top = $pageSize;
            $requestConfiguration->queryParameters->select = ['id', 'name', 'size', 'mimeType', 'lastModifiedDateTime', 'webUrl', 'folder', 'file', 'parentReference'];

            $response = $requestBuilder->get($requestConfiguration)->wait();
            $items = $response->getValue() ?? [];

            $formattedFiles = [];
            foreach ($items as $item) {
                $formattedFiles[] = $this->formatFileData($item);
            }

            return [
                'files' => $formattedFiles,
                'nextPageToken' => $response->getOdataNextLink() ? 'has_more' : null
            ];
        } catch (\Exception $e) {
            throw new \Exception('Error listing files: ' . $e->getMessage());
        }
    }

    public function getFile(string $fileId): array
    {
        $this->ensureValidToken();

        if (!$this->graphClient) {
            $this->initializeService();
        }

        try {
            $requestBuilder = $this->graphClient->me()->drive()->items()->byDriveItemId($fileId);

            $requestConfiguration = new \Microsoft\Graph\Generated\Me\Drive\Items\Item\DriveItemItemRequestBuilderGetRequestConfiguration();
            $requestConfiguration->queryParameters = new \Microsoft\Graph\Generated\Me\Drive\Items\Item\DriveItemItemRequestBuilderGetQueryParameters();
            $requestConfiguration->queryParameters->select = ['id', 'name', 'size', 'mimeType', 'lastModifiedDateTime', 'webUrl', 'folder', 'file', 'parentReference'];

            $item = $requestBuilder->get($requestConfiguration)->wait();
            return $this->formatFileData($item);
        } catch (\Exception $e) {
            throw new \Exception('Error getting file: ' . $e->getMessage());
        }
    }

    public function searchFiles(string $query, string $folderId = 'root'): array
    {
        $this->ensureValidToken();

        if (!$this->graphClient) {
            $this->initializeService();
        }

        try {
            $requestBuilder = $this->graphClient->me()->drive()->search($query);

            $response = $requestBuilder->get()->wait();
            $items = $response->getValue() ?? [];

            $formattedFiles = [];
            foreach ($items as $item) {
                // Filter by folder if specified
                if ($folderId !== 'root') {
                    $parentReference = $item->getParentReference();
                    if (!$parentReference || $parentReference->getId() !== $folderId) {
                        continue;
                    }
                }
                $formattedFiles[] = $this->formatFileData($item);
            }

            return $formattedFiles;
        } catch (\Exception $e) {
            throw new \Exception('Error searching files: ' . $e->getMessage());
        }
    }

    public function getBreadcrumb(string $folderId): array
    {
        if ($folderId === 'root') {
            return [['id' => 'root', 'name' => 'OneDrive']];
        }

        try {
            if (!$this->graphClient) {
                $this->initializeService();
            }

            $breadcrumb = [];
            $currentId = $folderId;

            // Build breadcrumb by traversing up the parent chain
            while ($currentId && $currentId !== 'root') {
                $requestBuilder = $this->graphClient->me()->drive()->items()->byDriveItemId($currentId);

                $requestConfiguration = new \Microsoft\Graph\Generated\Me\Drive\Items\Item\DriveItemItemRequestBuilderGetRequestConfiguration();
                $requestConfiguration->queryParameters = new \Microsoft\Graph\Generated\Me\Drive\Items\Item\DriveItemItemRequestBuilderGetQueryParameters();
                $requestConfiguration->queryParameters->select = ['id', 'name', 'parentReference'];

                $item = $requestBuilder->get($requestConfiguration)->wait();

                array_unshift($breadcrumb, [
                    'id' => $item->getId(),
                    'name' => $item->getName()
                ]);

                $parentReference = $item->getParentReference();
                $currentId = $parentReference ? $parentReference->getId() : null;
            }

            // Add root at the beginning
            array_unshift($breadcrumb, ['id' => 'root', 'name' => 'OneDrive']);

            return $breadcrumb;
        } catch (\Exception) {
            return [['id' => 'root', 'name' => 'OneDrive']];
        }
    }

    protected function performConnectionTest(): bool
    {
        try {
            if (!$this->graphClient) {
                $this->initializeService();
            }

            // Test connection by getting user's drive info
            $this->graphClient->me()->drive()->get()->wait();
            return true;
        } catch (\Exception) {
            return false;
        }
    }

    protected function formatFileData($item): array
    {
        if (!$item) {
            throw new \InvalidArgumentException('Expected DriveItem object for OneDrive file data');
        }

        $isFolder = $item->getFolder() !== null;
        $name = $item->getName() ?? 'Unknown';
        $id = $item->getId() ?? '';
        $size = $item->getSize() ?? 0;
        $mimeType = $item->getMimeType() ?? ($isFolder ? $this->getFolderMimeType() : 'application/octet-stream');
        $webUrl = $item->getWebUrl();
        $lastModified = $item->getLastModifiedDateTime();

        // Generate download URL for files
        $downloadUrl = null;
        if (!$isFolder && $webUrl) {
            // OneDrive download URL pattern
            $downloadUrl = str_replace('?web=1', '?download=1', $webUrl);
        }

        return $this->getStandardFileStructure(
            $id,
            $name,
            $mimeType,
            $isFolder,
            [
                'size' => $size,
                'sizeFormatted' => $this->formatFileSize($size),
                'modifiedTime' => $lastModified ? $lastModified->format('c') : null,
                'webViewLink' => $webUrl,
                'webContentLink' => $downloadUrl,
                'iconLink' => null, // OneDrive doesn't provide direct icon links
                'parents' => $this->getParentIds($item),
                'downloadUrl' => $downloadUrl
            ]
        );
    }

    protected function getFolderMimeType(): string
    {
        return 'application/vnd.onedrive.folder';
    }

    private function formatFileSize(int $bytes): string
    {
        if ($bytes === 0) return '0 B';

        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $power = floor(log($bytes, 1024));
        $power = min($power, count($units) - 1);

        $size = $bytes / pow(1024, $power);
        $formattedSize = round($size, 2);

        return $formattedSize . ' ' . $units[$power];
    }

    private function getParentIds($item): array
    {
        $parentReference = $item->getParentReference();
        if (!$parentReference || !$parentReference->getId()) {
            return [];
        }

        return [$parentReference->getId()];
    }

    public function getSupportedMimeTypes(): array
    {
        return [
            // Documents
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.ms-powerpoint',
            'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            'text/plain',
            'text/csv',
            'application/rtf',

            // Images
            'image/jpeg',
            'image/png',
            'image/gif',
            'image/bmp',
            'image/svg+xml',
            'image/tiff',
            'image/webp',

            // Videos
            'video/mp4',
            'video/avi',
            'video/mov',
            'video/wmv',
            'video/flv',
            'video/webm',
            'video/mkv',
            'video/quicktime',

            // Audio
            'audio/mp3',
            'audio/wav',
            'audio/ogg',
            'audio/aac',
            'audio/flac',
            'audio/wma',
            'audio/m4a',

            // Archives
            'application/zip',
            'application/x-rar-compressed',
            'application/x-7z-compressed',
            'application/x-tar',
            'application/gzip',

            // Code files
            'text/html',
            'text/css',
            'text/javascript',
            'application/json',
            'application/xml',
            'text/xml',

            // Folders
            'application/vnd.onedrive.folder'
        ];
    }
}
